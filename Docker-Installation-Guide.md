# Docker Installation Guide (Without Docker Desktop)

This guide provides multiple methods to install Docker on Windows without Docker Desktop.

## Prerequisites

- Windows 10/11 (64-bit)
- Administrator privileges
- Internet connection

## Method 1: Chocolatey Installation (Recommended)

### Step 1: Open PowerShell as Administrator
- Press `Win + X` and select "Windows PowerShell (Admin)" or "Terminal (Admin)"

### Step 2: Set Execution Policy
```powershell
Set-ExecutionPolicy Bypass -Scope Process -Force
```

### Step 3: Install Chocolatey
```powershell
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
```

### Step 4: Install Docker
```powershell
choco install docker-engine -y
choco install docker-compose -y
```

### Step 5: Start Docker Service
```powershell
Start-Service docker
```

## Method 2: Automated Script Installation

### Step 1: Run the Installation Script
```powershell
# Navigate to your project directory
cd "C:\Users\<USER>\OneDrive\Desktop\spring groovy\jenkins_pipeline"

# Run the installation script as Administrator
.\install-docker.ps1
```

### Step 2: Restart Computer
After the script completes, restart your computer.

### Step 3: Verify Installation
```powershell
docker --version
docker-compose --version
```

## Method 3: WSL2 Installation

### Step 1: Run WSL2 Setup Script
```powershell
.\install-docker-wsl.ps1
```

### Step 2: Complete WSL2 Setup
1. Restart your computer
2. Open Ubuntu from Start menu
3. Create username and password
4. Install Docker in Ubuntu:
```bash
sudo apt update
sudo apt install docker.io docker-compose -y
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

### Step 3: Use Docker from WSL2
```bash
# In Ubuntu terminal
docker --version
docker-compose --version
```

## Method 4: Manual Installation

### Step 1: Enable Windows Features
```powershell
# Run as Administrator
Enable-WindowsOptionalFeature -Online -FeatureName containers -All
Enable-WindowsOptionalFeature -Online -FeatureName Microsoft-Hyper-V -All
```

### Step 2: Download Docker Engine
1. Go to https://download.docker.com/win/static/stable/x86_64/
2. Download the latest docker-XX.X.X.zip file
3. Extract to `C:\Program Files\Docker`

### Step 3: Add to PATH
```powershell
$env:PATH += ";C:\Program Files\Docker"
[Environment]::SetEnvironmentVariable("PATH", $env:PATH, "Machine")
```

### Step 4: Install as Service
```powershell
& "C:\Program Files\Docker\dockerd.exe" --register-service
Start-Service docker
```

## Verification

After installation, verify Docker is working:

```powershell
# Check Docker version
docker --version

# Check Docker Compose version
docker-compose --version
# or
docker compose --version

# Test Docker installation
docker run hello-world

# Check Docker service status
Get-Service docker
```

## Running Your Spring Boot Application

Once Docker is installed, you can run your application:

```powershell
# Navigate to your project directory
cd "C:\Users\<USER>\OneDrive\Desktop\spring groovy\jenkins_pipeline"

# Build and run with Docker Compose
docker compose up --build

# Or if using older docker-compose
docker-compose up --build
```

## Troubleshooting

### Common Issues

1. **"docker: command not found"**
   - Restart PowerShell/Terminal
   - Check if Docker is in PATH: `$env:PATH`
   - Restart computer if needed

2. **"Access denied" errors**
   - Run PowerShell as Administrator
   - Check Windows features are enabled

3. **Service won't start**
   ```powershell
   # Check service status
   Get-Service docker
   
   # Start service manually
   Start-Service docker
   
   # Check for errors
   Get-EventLog -LogName Application -Source Docker
   ```

4. **WSL2 issues**
   - Ensure virtualization is enabled in BIOS
   - Update Windows to latest version
   - Install WSL2 kernel update manually

### Alternative: Podman

If Docker installation fails, you can use Podman as an alternative:

```powershell
# Install Podman via Chocolatey
choco install podman-desktop -y

# Use podman instead of docker
podman --version
```

## Next Steps

After successful installation:
1. Verify Docker is working with `docker run hello-world`
2. Navigate to your Spring Boot project directory
3. Run `docker compose up --build` to start your application
4. Access your app at http://localhost:8089
