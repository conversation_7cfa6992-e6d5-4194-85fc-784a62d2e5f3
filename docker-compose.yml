version: '3.8'

services:
  # MySQL Database Service
  mysql:
    image: mysql:8.0
    container_name: smarthrms-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: smarthrms
      MYSQL_USER: root
      MYSQL_PASSWORD: root
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Spring Boot Application Service
  app:
    build: .
    container_name: spring-boot-app
    environment:
      SPRING_DATASOURCE_URL: *********************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: root
      SPRING_DATASOURCE_DRIVER_CLASS_NAME: com.mysql.cj.jdbc.Driver
    ports:
      - "8089:8089"
    depends_on:
      mysql:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped

volumes:
  mysql_data:

networks:
  app-network:
    driver: bridge
